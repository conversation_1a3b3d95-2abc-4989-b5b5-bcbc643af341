# Mist - In-Memory MySQL Database

Mist is an in-memory MySQL-compatible database written in Go, using the TiDB parser for SQL parsing. It supports basic SQL operations including CREATE TABLE, INSERT, and SELECT with WHERE clauses.

## Features

- **CREATE TABLE** - Create tables with various column types
- **ALTER TABLE** - Add, drop, modify, and rename columns
- **INSERT** - Insert data into tables
- **SELECT** - Query data with column selection and WHERE clauses
- **UPDATE** - Update existing rows with arithmetic operations
- **DELETE** - Delete rows based on conditions
- **JOIN** - Inner joins between tables with ON conditions
- **SUBQUERIES** - Support for subqueries in FROM clause
- **LIMIT** - Limit results with offset and count (<PERSON><PERSON><PERSON> offset, count)
- **AGGREGATE FUNCTIONS** - COUNT, SUM, AVG, MIN, MAX with support for DISTINCT
- **INDEXES** - Hash-based indexes for query optimization
- **SHOW TABLES** - List all tables in the database
- **SHOW INDEX** - List indexes for tables
- **Thread-safe** - Concurrent access to tables is handled safely
- **MySQL-compatible syntax** - Uses TiDB parser for accurate MySQL SQL parsing

### Supported Column Types

- `INT` - Integer values
- `VARCHAR(length)` - Variable-length strings
- `TEXT` - Text data
- `FLOAT` - Floating-point numbers
- `BOOL` - Boolean values

### Supported SQL Statements

#### CREATE TABLE
```sql
CREATE TABLE users (
    id INT PRIMARY KEY,
    name VARCHAR(50),
    age INT,
    email VARCHAR(100)
);

CREATE TABLE IF NOT EXISTS products (
    id INT,
    name VARCHAR(100),
    price FLOAT,
    in_stock BOOL
);
```

#### ALTER TABLE
```sql
-- Add a new column
ALTER TABLE users ADD COLUMN phone VARCHAR(20);

-- Drop a column
ALTER TABLE users DROP COLUMN phone;

-- Modify column type
ALTER TABLE users MODIFY COLUMN age BIGINT;

-- Rename and modify column
ALTER TABLE users CHANGE COLUMN email email_address VARCHAR(150);
```

#### INSERT
```sql
INSERT INTO users VALUES (1, 'Alice', 30, '<EMAIL>');
INSERT INTO users (id, name, age) VALUES (2, 'Bob', 25);
```

#### SELECT
```sql
-- Select all columns
SELECT * FROM users;

-- Select specific columns
SELECT name, age FROM users;

-- Select with WHERE clause
SELECT * FROM users WHERE age > 25;
SELECT name FROM users WHERE age = 30;
SELECT * FROM products WHERE price > 100 AND in_stock = 1;

-- Select with JOIN
SELECT users.name, departments.name
FROM users
JOIN departments ON users.department_id = departments.id;

-- Select with LIMIT
SELECT * FROM users LIMIT 5;

-- Select with LIMIT and offset
SELECT * FROM users LIMIT 2, 3;  -- Skip 2 rows, return 3 rows

-- Select with WHERE and LIMIT
SELECT name, age FROM users WHERE age > 25 LIMIT 10;
```

#### SUBQUERIES
```sql
-- Subquery in FROM clause
SELECT name, salary FROM (SELECT * FROM users WHERE department_id = 1) AS dept_users;

-- Subquery with LIMIT
SELECT * FROM (SELECT * FROM users ORDER BY salary DESC) AS sorted_users LIMIT 5;
```

#### UPDATE
```sql
-- Update specific rows
UPDATE users SET age = 31 WHERE name = 'Alice';

-- Update with arithmetic operations
UPDATE products SET price = price * 1.1 WHERE category = 'electronics';

-- Update multiple columns
UPDATE users SET age = age + 1, name = 'Alice Smith' WHERE id = 1;

-- Update all rows
UPDATE products SET in_stock = 1;
```

#### DELETE
```sql
-- Delete specific rows
DELETE FROM users WHERE age < 18;

-- Delete with complex conditions
DELETE FROM products WHERE price > 1000 AND in_stock = 0;

-- Delete all rows (use with caution!)
DELETE FROM temp_table;
```

#### JOIN
```sql
-- Inner join with ON condition
SELECT u.name, d.name
FROM users u
JOIN departments d ON u.department_id = d.id;

-- Join with WHERE clause
SELECT users.name, departments.name, users.salary
FROM users
JOIN departments ON users.department_id = departments.id
WHERE users.salary > 50000;

-- Join with aggregates
SELECT COUNT(*) FROM users JOIN departments ON users.department_id = departments.id;
```

#### AGGREGATE FUNCTIONS
```sql
-- Count all rows
SELECT COUNT(*) FROM users;

-- Count non-null values in a column
SELECT COUNT(email) FROM users;

-- Count distinct values
SELECT COUNT(DISTINCT department_id) FROM users;

-- Sum, average, min, max
SELECT SUM(salary), AVG(salary), MIN(salary), MAX(salary) FROM users;

-- Aggregates with WHERE clause
SELECT COUNT(*) FROM users WHERE age > 30;
```

#### INDEXES
```sql
-- Create an index
CREATE INDEX idx_age ON users (age);

-- Create index on different column
CREATE INDEX idx_salary ON users (salary);

-- Drop an index
DROP INDEX idx_age;

-- Show indexes for a table
SHOW INDEX FROM users;
```

#### SHOW COMMANDS
```sql
-- Show all tables
SHOW TABLES;

-- Show indexes for a specific table
SHOW INDEX FROM table_name;
```

## Usage

### Demo Mode (Default)
Run the demo to see Mist in action:
```bash
go run .
```

### Interactive Mode
Start an interactive SQL session:
```bash
go run . -i
```

In interactive mode:
- Type SQL statements and end them with semicolon (`;`)
- Type `help` for command reference
- Type `exit` or `quit` to exit
- Type `clear` to clear the screen

### Example Interactive Session
```
mist> CREATE TABLE users (id INT, name VARCHAR(50), age INT, salary FLOAT);
Table users created successfully

mist> CREATE INDEX idx_age ON users (age);
Index created successfully

mist> INSERT INTO users VALUES (1, 'Alice', 30, 75000);
Insert successful

mist> INSERT INTO users VALUES (2, 'Bob', 25, 65000);
Insert successful

mist> SELECT COUNT(*) FROM users;
| COUNT(*)        |
|-----------------|
| 2               |

mist> SELECT AVG(salary) FROM users WHERE age > 25;
| AVG(salary)     |
|-----------------|
| 75000           |

mist> SHOW INDEX FROM users;
| Index_Name      | Table           | Column          | Type            |
|--------------------------------------------------------------------|
| idx_age         | users           | age             | HASH            |

mist> UPDATE users SET salary = salary * 1.1 WHERE age > 28;
Updated 1 row(s)

mist> SELECT name, salary FROM users;
| name            | salary          |
|----------------------------------|
| Alice           | 82500           |
| Bob             | 65000           |

mist> exit
Goodbye!
```

## Architecture

The database consists of several key components:

- **SQLEngine** - Main coordinator that routes SQL statements to appropriate handlers
- **Database** - Container for tables with thread-safe operations
- **Table** - Individual table with columns and rows
- **Parser Integration** - Uses TiDB parser for MySQL-compatible SQL parsing

### File Structure

- `main.go` - Entry point with demo and interactive modes
- `engine.go` - Main SQL engine and coordinator
- `database.go` - Core data structures (Database, Table, Column, Row)
- `create_table.go` - CREATE TABLE statement handler
- `alter_table.go` - ALTER TABLE statement handler
- `insert.go` - INSERT statement handler
- `select.go` - SELECT statement handler with LIMIT and subquery support
- `update.go` - UPDATE statement handler
- `delete.go` - DELETE statement handler
- `join.go` - JOIN operations handler
- `aggregate.go` - Aggregate functions (COUNT, SUM, AVG, MIN, MAX)
- `index.go` - Index management and optimization
- `index_commands.go` - CREATE/DROP INDEX statement handlers
- `engine_test.go` - Comprehensive test suite

## Testing

Run the test suite:
```bash
go test -v
```

The tests cover:
- Table creation with various column types
- ALTER TABLE operations (ADD, DROP, MODIFY, CHANGE columns)
- Data insertion and validation
- SELECT queries with WHERE clauses
- LIMIT clause with offset and count
- Subqueries in FROM clause
- UPDATE statements with arithmetic operations
- DELETE statements with conditions
- JOIN operations between tables
- Aggregate functions (COUNT, SUM, AVG, MIN, MAX)
- Index creation, usage, and management
- Query optimization with indexes
- Error handling for invalid operations
- SHOW TABLES and SHOW INDEX functionality

## Dependencies

- **TiDB Parser** - For MySQL-compatible SQL parsing
- **Go Standard Library** - For core functionality

## Limitations

Current limitations (future enhancement opportunities):
- No persistent storage (in-memory only)
- Limited JOIN support (only INNER JOIN implemented)
- No GROUP BY clause (aggregates work on entire result set)
- Limited subquery support (only in FROM clause)
- Basic hash indexes only (no B-tree or other index types)
- No transactions
- No user authentication
- No network protocol support
- No foreign key constraints
- No ORDER BY clause
- No HAVING clause

## Building

Build the executable:
```bash
go build .
```

Run the executable:
```bash
# Demo mode
./mist

# Interactive mode
./mist -i
```

## Contributing

This is a learning project demonstrating how to build a SQL database engine. Feel free to extend it with additional features like:

- LEFT/RIGHT/OUTER JOIN operations
- ORDER BY and GROUP BY clauses
- HAVING clause for aggregate filtering
- More complex subqueries (WHERE, SELECT clauses)
- Common Table Expressions (CTEs)
- B-tree and other index types
- Persistent storage (file-based or embedded)
- Transactions and ACID compliance
- Network protocol support (MySQL wire protocol)
- Views and stored procedures
- Foreign key constraints

## License

This project is for educational purposes.
