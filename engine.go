package main

import (
	"fmt"
	"strings"

	"github.com/pingcap/tidb/pkg/parser/ast"
)

// SQLEngine represents the main SQL execution engine
type SQLEngine struct {
	database *Database
}

// NewSQLEngine creates a new SQL engine with an empty database
func NewSQLEngine() *SQLEngine {
	return &SQLEngine{
		database: NewDatabase(),
	}
}

// Execute executes a SQL statement and returns the result
func (engine *SQLEngine) Execute(sql string) (interface{}, error) {
	// Trim whitespace and ensure statement ends with semicolon for parsing
	sql = strings.TrimSpace(sql)
	if !strings.HasSuffix(sql, ";") {
		sql += ";"
	}

	// Handle special cases that might not parse well with TiDB parser
	if isCreateIndexStatement(sql) {
		err := parseCreateIndexSQL(engine.database, sql)
		if err != nil {
			return nil, err
		}
		return "Index created successfully", nil
	}

	if isDropIndexStatement(sql) {
		err := parseDropIndexSQL(engine.database, sql)
		if err != nil {
			return nil, err
		}
		return "Index dropped successfully", nil
	}

	if isShowIndexStatement(sql) {
		result, err := parseShowIndexSQL(engine.database, sql)
		if err != nil {
			return nil, err
		}
		return result, nil
	}

	// Parse the SQL statement
	astNode, err := parse(sql)
	if err != nil {
		return nil, fmt.Errorf("parse error: %v", err)
	}

	// Route to appropriate handler based on statement type
	switch stmt := (*astNode).(type) {
	case *ast.CreateTableStmt:
		err := ExecuteCreateTable(engine.database, stmt)
		if err != nil {
			return nil, err
		}
		return fmt.Sprintf("Table %s created successfully", stmt.Table.Name.String()), nil

	case *ast.InsertStmt:
		err := ExecuteInsert(engine.database, stmt)
		if err != nil {
			return nil, err
		}
		return "Insert successful", nil

	case *ast.SelectStmt:
		// Check if this is a JOIN query
		if engine.isJoinQuery(stmt) {
			result, err := ExecuteSelectWithJoin(engine.database, stmt)
			if err != nil {
				return nil, err
			}
			return result, nil
		} else {
			result, err := ExecuteSelect(engine.database, stmt)
			if err != nil {
				return nil, err
			}
			return result, nil
		}

	case *ast.UpdateStmt:
		count, err := ExecuteUpdate(engine.database, stmt)
		if err != nil {
			return nil, err
		}
		return fmt.Sprintf("Updated %d row(s)", count), nil

	case *ast.DeleteStmt:
		count, err := ExecuteDelete(engine.database, stmt)
		if err != nil {
			return nil, err
		}
		return fmt.Sprintf("Deleted %d row(s)", count), nil

	case *ast.AlterTableStmt:
		err := ExecuteAlterTable(engine.database, stmt)
		if err != nil {
			return nil, err
		}
		return fmt.Sprintf("Table %s altered successfully", stmt.Table.Name.String()), nil

	case *ast.ShowStmt:
		return engine.executeShow(stmt)

	default:
		return nil, fmt.Errorf("unsupported statement type: %T", stmt)
	}
}

// isJoinQuery checks if a SELECT statement contains a JOIN
func (engine *SQLEngine) isJoinQuery(stmt *ast.SelectStmt) bool {
	if stmt.From == nil || stmt.From.TableRefs == nil {
		return false
	}

	// Check if TableRefs has a Right side (indicating a JOIN)
	if stmt.From.TableRefs.Right != nil {
		return true
	}

	// Check for comma-separated tables (cross join)
	// In this case, Left is a Join with Tp=0 and Right is nil
	if join, ok := stmt.From.TableRefs.Left.(*ast.Join); ok {
		if join.Tp == 0 && join.Right == nil {
			return true
		}
	}

	return false
}

// executeShow handles SHOW statements
func (engine *SQLEngine) executeShow(stmt *ast.ShowStmt) (interface{}, error) {
	switch stmt.Tp {
	case ast.ShowTables:
		tables := engine.database.ListTables()
		result := &SelectResult{
			Columns: []string{"Tables"},
			Rows:    make([][]interface{}, len(tables)),
		}
		for i, table := range tables {
			result.Rows[i] = []interface{}{table}
		}
		return result, nil

	default:
		return nil, fmt.Errorf("unsupported SHOW statement type: %v", stmt.Tp)
	}
}

// ExecuteMultiple executes multiple SQL statements separated by semicolons
func (engine *SQLEngine) ExecuteMultiple(sql string) ([]interface{}, error) {
	// Split by semicolon and execute each statement
	statements := strings.Split(sql, ";")
	results := make([]interface{}, 0)

	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" {
			continue
		}

		result, err := engine.Execute(stmt)
		if err != nil {
			return results, err
		}
		results = append(results, result)
	}

	return results, nil
}

// GetDatabase returns the underlying database (for testing)
func (engine *SQLEngine) GetDatabase() *Database {
	return engine.database
}

// PrintResult prints the result of a SQL execution in a user-friendly format
func PrintResult(result interface{}) {
	switch r := result.(type) {
	case *SelectResult:
		PrintSelectResult(r)
	case string:
		fmt.Println(r)
	default:
		fmt.Printf("Result: %v\n", r)
	}
}

// Interactive mode for the SQL engine
func (engine *SQLEngine) Interactive() {
	fmt.Println("Mist In-Memory MySQL Database")
	fmt.Println("Type 'exit' or 'quit' to exit")
	fmt.Println("Type 'help' for help")
	fmt.Println("End statements with semicolon (;)")
	fmt.Println()

	var inputBuffer strings.Builder

	for {
		if inputBuffer.Len() == 0 {
			fmt.Print("mist> ")
		} else {
			fmt.Print("   -> ")
		}

		var line string
		fmt.Scanln(&line)

		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// Check for special commands
		if inputBuffer.Len() == 0 {
			switch strings.ToLower(line) {
			case "exit", "quit":
				fmt.Println("Goodbye!")
				return
			case "help":
				engine.printHelp()
				continue
			case "clear":
				fmt.Print("\033[2J\033[H") // Clear screen
				continue
			}
		}

		// Add line to buffer
		if inputBuffer.Len() > 0 {
			inputBuffer.WriteString(" ")
		}
		inputBuffer.WriteString(line)

		// Check if statement is complete (ends with semicolon)
		if strings.HasSuffix(line, ";") {
			input := inputBuffer.String()
			inputBuffer.Reset()

			result, err := engine.Execute(input)
			if err != nil {
				fmt.Printf("Error: %v\n", err)
			} else {
				PrintResult(result)
			}
			fmt.Println()
		}
	}
}

// printHelp prints help information
func (engine *SQLEngine) printHelp() {
	fmt.Println("Supported SQL statements:")
	fmt.Println("  CREATE TABLE table_name (column_name column_type, ...);")
	fmt.Println("  ALTER TABLE table_name ADD COLUMN column_name column_type;")
	fmt.Println("  ALTER TABLE table_name DROP COLUMN column_name;")
	fmt.Println("  ALTER TABLE table_name MODIFY COLUMN column_name new_type;")
	fmt.Println("  INSERT INTO table_name VALUES (value1, value2, ...);")
	fmt.Println("  INSERT INTO table_name (col1, col2) VALUES (val1, val2);")
	fmt.Println("  SELECT * FROM table_name;")
	fmt.Println("  SELECT col1, col2 FROM table_name WHERE condition LIMIT 10;")
	fmt.Println("  SELECT col1, col2 FROM table_name LIMIT 5, 10;")
	fmt.Println("  SELECT COUNT(*), SUM(col), AVG(col) FROM table_name;")
	fmt.Println("  SELECT * FROM table1 JOIN table2 ON condition;")
	fmt.Println("  SELECT * FROM (SELECT * FROM table1) AS subquery;")
	fmt.Println("  UPDATE table_name SET col1 = value1 WHERE condition;")
	fmt.Println("  DELETE FROM table_name WHERE condition;")
	fmt.Println("  CREATE INDEX index_name ON table_name (column_name);")
	fmt.Println("  DROP INDEX index_name;")
	fmt.Println("  SHOW TABLES;")
	fmt.Println("  SHOW INDEX FROM table_name;")
	fmt.Println()
	fmt.Println("Supported column types:")
	fmt.Println("  INT, VARCHAR(length), TEXT, FLOAT, BOOL")
	fmt.Println()
	fmt.Println("Supported aggregate functions:")
	fmt.Println("  COUNT(*), COUNT(column), SUM(column), AVG(column), MIN(column), MAX(column)")
	fmt.Println()
	fmt.Println("LIMIT clause:")
	fmt.Println("  LIMIT count - limit to 'count' rows")
	fmt.Println("  LIMIT offset, count - skip 'offset' rows, then return 'count' rows")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(50), age INT);")
	fmt.Println("  ALTER TABLE users ADD COLUMN email VARCHAR(100);")
	fmt.Println("  CREATE INDEX idx_age ON users (age);")
	fmt.Println("  INSERT INTO users VALUES (1, 'Alice', 30, '<EMAIL>');")
	fmt.Println("  SELECT * FROM users WHERE age > 25 LIMIT 5;")
	fmt.Println("  SELECT COUNT(*) FROM users WHERE age > 25;")
	fmt.Println("  SELECT AVG(age) FROM users;")
	fmt.Println("  UPDATE users SET age = age + 1 WHERE name = 'Alice';")
	fmt.Println("  DELETE FROM users WHERE age < 18;")
	fmt.Println("  SELECT u.name, p.title FROM users u JOIN posts p ON u.id = p.user_id LIMIT 10;")
}
