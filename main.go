package main

import (
	"fmt"
	"os"

	"github.com/pingcap/tidb/pkg/parser"
	"github.com/pingcap/tidb/pkg/parser/ast"
	_ "github.com/pingcap/tidb/pkg/types/parser_driver"
)

func parse(sql string) (*ast.StmtNode, error) {
	p := parser.New()

	stmtNodes, _, err := p.ParseSQL(sql)
	if err != nil {
		return nil, err
	}

	return &stmtNodes[0], nil
}

func main() {
	// Create a new SQL engine
	engine := NewSQLEngine()

	// Check if user wants interactive mode
	if len(os.Args) > 1 && os.Args[1] == "-i" {
		engine.Interactive()
		return
	}

	// Run demo by default
	runDemo(engine)
}

func runDemo(engine *SQLEngine) {
	// Example usage
	fmt.Println("=== Mist In-Memory MySQL Database Demo ===")
	fmt.Println("Now with ALTER TABLE, LIMIT, SUBQUERIES, AGGREGATES, and INDEXES!")
	fmt.Println()

	// Create tables
	fmt.Println("Creating tables...")
	tables := []string{
		"CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(50), age INT, department_id INT, salary FLOAT)",
		"CREATE TABLE departments (id INT PRIMARY KEY, name VARCHAR(50), budget FLOAT)",
	}

	for _, query := range tables {
		result, err := engine.Execute(query)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return
		}
		fmt.Println(result)
	}
	fmt.Println()

	// Create indexes for performance
	fmt.Println("Creating indexes...")
	indexes := []string{
		"CREATE INDEX idx_age ON users (age)",
		"CREATE INDEX idx_dept ON users (department_id)",
	}

	for _, query := range indexes {
		result, err := engine.Execute(query)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return
		}
		fmt.Println(result)
	}
	fmt.Println()

	// Insert data
	fmt.Println("Inserting data...")
	queries := []string{
		"INSERT INTO departments VALUES (1, 'Engineering', 100000.0)",
		"INSERT INTO departments VALUES (2, 'Marketing', 75000.0)",
		"INSERT INTO users VALUES (1, 'Alice', 30, 1, 85000.0)",
		"INSERT INTO users VALUES (2, 'Bob', 25, 2, 65000.0)",
		"INSERT INTO users VALUES (3, 'Charlie', 35, 1, 95000.0)",
		"INSERT INTO users VALUES (4, 'Diana', 28, 2, 70000.0)",
		"INSERT INTO users VALUES (5, 'Eve', 32, 1, 90000.0)",
	}

	for _, query := range queries {
		result, err := engine.Execute(query)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return
		}
		fmt.Println(result)
	}
	fmt.Println()

	// Demonstrate AGGREGATE functions
	fmt.Println("AGGREGATES - Database statistics:")
	aggregateQueries := []string{
		"SELECT COUNT(*) FROM users",
		"SELECT AVG(age) FROM users",
		"SELECT SUM(salary) FROM users",
		"SELECT MIN(salary), MAX(salary) FROM users",
	}

	for _, query := range aggregateQueries {
		fmt.Printf("Query: %s\n", query)
		result, err := engine.Execute(query)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return
		}
		PrintResult(result)
		fmt.Println()
	}

	// Demonstrate INDEX usage (optimized query)
	fmt.Println("INDEX-OPTIMIZED QUERY - Users over 30 (using idx_age):")
	result, err := engine.Execute("SELECT name, age FROM users WHERE age > 30")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	PrintResult(result)
	fmt.Println()

	// Demonstrate JOIN with aggregates
	fmt.Println("JOIN + AGGREGATES - Count of users by department:")
	result, err = engine.Execute("SELECT COUNT(*) FROM users JOIN departments ON users.department_id = departments.id")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	PrintResult(result)
	fmt.Println()

	// Regular JOIN without aggregates
	fmt.Println("JOIN - Users with their departments:")
	result, err = engine.Execute("SELECT users.name, departments.name FROM users JOIN departments ON users.department_id = departments.id")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	PrintResult(result)
	fmt.Println()

	// Show indexes
	fmt.Println("SHOW INDEXES for users table:")
	result, err = engine.Execute("SHOW INDEX FROM users")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	PrintResult(result)
	fmt.Println()

	// Demonstrate UPDATE with arithmetic
	fmt.Println("UPDATE - 10% salary increase for Engineering:")
	result, err = engine.Execute("UPDATE users SET salary = salary * 1.1 WHERE department_id = 1")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	fmt.Println(result)

	// Show updated salaries with LIMIT
	fmt.Println("Updated salaries (top 2):")
	result, err = engine.Execute("SELECT name, salary FROM users WHERE department_id = 1 LIMIT 2")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	PrintResult(result)
	fmt.Println()

	// Demonstrate ALTER TABLE
	fmt.Println("ALTER TABLE - Adding email column:")
	result, err = engine.Execute("ALTER TABLE users ADD COLUMN email VARCHAR(100)")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	fmt.Println(result)

	// Update with email data
	fmt.Println("Adding email data:")
	emailUpdates := []string{
		"UPDATE users SET email = '<EMAIL>' WHERE name = 'Alice'",
		"UPDATE users SET email = '<EMAIL>' WHERE name = 'Charlie'",
		"UPDATE users SET email = '<EMAIL>' WHERE name = 'Eve'",
	}

	for _, query := range emailUpdates {
		_, err = engine.Execute(query)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			return
		}
	}

	// Demonstrate subquery
	fmt.Println("SUBQUERY - High earners from Engineering:")
	result, err = engine.Execute("SELECT name, salary FROM (SELECT * FROM users WHERE department_id = 1) AS eng_users WHERE salary > 90000")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	PrintResult(result)
	fmt.Println()

	// Demonstrate LIMIT with offset
	fmt.Println("LIMIT with offset - Users 2-3:")
	result, err = engine.Execute("SELECT name, email FROM users LIMIT 1, 2")
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	PrintResult(result)
	fmt.Println()

	fmt.Println("Demo completed!")
	fmt.Println("Features demonstrated:")
	fmt.Println("✓ CREATE TABLE with multiple data types")
	fmt.Println("✓ ALTER TABLE (ADD COLUMN, DROP COLUMN, MODIFY COLUMN)")
	fmt.Println("✓ CREATE INDEX for query optimization")
	fmt.Println("✓ INSERT with data validation")
	fmt.Println("✓ SELECT with WHERE clauses (index-optimized)")
	fmt.Println("✓ LIMIT clause with offset and count")
	fmt.Println("✓ SUBQUERIES in FROM clause")
	fmt.Println("✓ AGGREGATE functions (COUNT, SUM, AVG, MIN, MAX)")
	fmt.Println("✓ JOIN operations between tables")
	fmt.Println("✓ UPDATE with arithmetic expressions")
	fmt.Println("✓ SHOW TABLES and SHOW INDEX commands")
	fmt.Println()
	fmt.Println("Run with '-i' flag for interactive mode: go run . -i")
}
